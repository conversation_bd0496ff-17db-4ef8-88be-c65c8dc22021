import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import {
  ExpenseProvider,
  GroupProvider,
  CurrencyProvider,
  ThemeProvider,
  ToastProvider
} from './context';

// Pages
import Home from './pages/Home';
import Group from './pages/Group';
import Expenses from './pages/Expenses';
import Dashboard from './pages/Dashboard';
import Settings from './pages/Settings';

function App() {
  return (
    <ThemeProvider>
      <CurrencyProvider>
        <ToastProvider>
          <GroupProvider>
            <ExpenseProvider>
              <Router>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/group" element={<Group />} />
                  <Route path="/expenses" element={<Expenses />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/settings" element={<Settings />} />
                </Routes>
              </Router>
            </ExpenseProvider>
          </GroupProvider>
        </ToastProvider>
      </CurrencyProvider>
    </ThemeProvider>
  );
}

export default App
