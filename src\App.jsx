import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { AppLayout } from './components/layout';
import {
  ExpenseProvider,
  GroupProvider,
  CurrencyProvider,
  ThemeProvider,
  ToastProvider
} from './context';

// Pages
import Home from './pages/Home';
import Groups from './pages/Groups';
import Group from './pages/Group';
import Expenses from './pages/Expenses';
import Dashboard from './pages/Dashboard';
import Settings from './pages/Settings';

function App() {
  return (
    <ThemeProvider>
      <CurrencyProvider>
        <ToastProvider>
          <GroupProvider>
            <ExpenseProvider>
              <Router>
                <AnimatePresence mode="wait">
                  <Routes>
                    <Route path="/" element={<AppLayout />}>
                      <Route index element={<Home />} />
                      <Route path="groups" element={<Groups />} />
                      <Route path="group" element={<Group />} />
                      <Route path="expenses" element={<Expenses />} />
                      <Route path="dashboard" element={<Dashboard />} />
                      <Route path="settings" element={<Settings />} />
                    </Route>
                  </Routes>
                </AnimatePresence>
              </Router>
            </ExpenseProvider>
          </GroupProvider>
        </ToastProvider>
      </CurrencyProvider>
    </ThemeProvider>
  );
}

export default App
