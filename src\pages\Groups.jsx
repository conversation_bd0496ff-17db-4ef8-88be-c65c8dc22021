import { motion } from 'framer-motion';
import { useState } from 'react';
import { Plus, Users, Settings, Trash2, Edit } from 'lucide-react';
import { GlassCard, GlassButton, Modal, Input } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useGroup, useToast } from '../context';

const Groups = () => {
  const { groups, addGroup, deleteGroup, setActiveGroup } = useGroup();
  const { showSuccess, showError } = useToast();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      showError('Group name is required');
      return;
    }

    const group = {
      name: newGroupName.trim(),
      description: newGroupDescription.trim(),
      members: [],
    };

    addGroup(group);
    showSuccess('Group created successfully!');
    setIsCreateModalOpen(false);
    setNewGroupName('');
    setNewGroupDescription('');
  };

  const handleDeleteGroup = (groupId, groupName) => {
    if (window.confirm(`Are you sure you want to delete "${groupName}"?`)) {
      deleteGroup(groupId);
      showSuccess('Group deleted successfully');
    }
  };

  return (
    <PageWrapper 
      title="Groups" 
      subtitle="Manage your expense groups and members"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Create Group Button */}
        <div className="mb-8">
          <GlassButton
            variant="primary"
            onClick={() => setIsCreateModalOpen(true)}
            className="w-full sm:w-auto"
          >
            <Plus className="w-5 h-5 mr-2" />
            Create New Group
          </GlassButton>
        </div>

        {/* Groups Grid */}
        {groups.length === 0 ? (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No groups yet
            </h3>
            <p className="text-gray-500 mb-6">
              Create your first group to start splitting expenses with friends.
            </p>
            <GlassButton
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Your First Group
            </GlassButton>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {groups.map((group, index) => (
              <motion.div
                key={group.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <GlassCard className="h-full">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {group.name}
                      </h3>
                      {group.description && (
                        <p className="text-gray-600 text-sm mb-3">
                          {group.description}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <GlassButton
                        variant="ghost"
                        size="sm"
                        onClick={() => setActiveGroup(group)}
                      >
                        <Settings className="w-4 h-4" />
                      </GlassButton>
                      <GlassButton
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteGroup(group.id, group.name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </GlassButton>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-500">
                      <Users className="w-4 h-4 mr-1" />
                      <span className="text-sm">
                        {group.members.length} member{group.members.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <GlassButton
                      variant="secondary"
                      size="sm"
                      onClick={() => setActiveGroup(group)}
                    >
                      Manage
                    </GlassButton>
                  </div>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        )}

        {/* Create Group Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Create New Group"
        >
          <div className="space-y-4">
            <Input
              label="Group Name"
              placeholder="Enter group name"
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
            />
            <Input
              label="Description (Optional)"
              placeholder="Enter group description"
              value={newGroupDescription}
              onChange={(e) => setNewGroupDescription(e.target.value)}
            />
            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="secondary"
                onClick={() => setIsCreateModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleCreateGroup}
                className="flex-1"
              >
                Create Group
              </GlassButton>
            </div>
          </div>
        </Modal>
      </div>
    </PageWrapper>
  );
};

export default Groups;
