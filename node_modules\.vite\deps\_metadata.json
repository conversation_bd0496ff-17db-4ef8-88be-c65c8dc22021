{"hash": "00c7572d", "configHash": "dcc07be4", "lockfileHash": "aa03dd42", "browserHash": "d8c234c6", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f5f43f30", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7bcd7d36", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3be97e4d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f5ceaf3c", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "bff26da4", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "91c16e20", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "99c104e6", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "4f641cc5", "needsInterop": false}}, "chunks": {"chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}