{"hash": "00c7572d", "configHash": "dcc07be4", "lockfileHash": "aa03dd42", "browserHash": "d8c234c6", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "26086ae2", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c782a663", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "aacfd607", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "829b9191", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "95c97114", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1174e9dd", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7aa47bdb", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "af6caead", "needsInterop": false}}, "chunks": {"chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}