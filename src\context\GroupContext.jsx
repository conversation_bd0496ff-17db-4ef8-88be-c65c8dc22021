import { createContext, useContext, useReducer, useEffect } from 'react';

const GroupContext = createContext();

// Action types
const GROUP_ACTIONS = {
  SET_GROUPS: 'SET_GROUPS',
  ADD_GROUP: 'ADD_GROUP',
  UPDATE_GROUP: 'UPDATE_GROUP',
  DELETE_GROUP: 'DELETE_GROUP',
  SET_ACTIVE_GROUP: 'SET_ACTIVE_GROUP',
  ADD_MEMBER: 'ADD_MEMBER',
  REMOVE_MEMBER: 'REMOVE_MEMBER',
  UPDATE_MEMBER: 'UPDATE_MEMBER',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
};

// Initial state
const initialState = {
  groups: [],
  activeGroup: null,
  loading: false,
  error: null,
};

// Reducer
const groupReducer = (state, action) => {
  switch (action.type) {
    case GROUP_ACTIONS.SET_GROUPS:
      return {
        ...state,
        groups: action.payload,
        loading: false,
        error: null,
      };
    case GROUP_ACTIONS.ADD_GROUP:
      return {
        ...state,
        groups: [...state.groups, action.payload],
        loading: false,
        error: null,
      };
    case GROUP_ACTIONS.UPDATE_GROUP:
      return {
        ...state,
        groups: state.groups.map(group =>
          group.id === action.payload.id ? action.payload : group
        ),
        activeGroup: state.activeGroup?.id === action.payload.id ? action.payload : state.activeGroup,
        loading: false,
        error: null,
      };
    case GROUP_ACTIONS.DELETE_GROUP:
      return {
        ...state,
        groups: state.groups.filter(group => group.id !== action.payload),
        activeGroup: state.activeGroup?.id === action.payload ? null : state.activeGroup,
        loading: false,
        error: null,
      };
    case GROUP_ACTIONS.SET_ACTIVE_GROUP:
      return {
        ...state,
        activeGroup: action.payload,
      };
    case GROUP_ACTIONS.ADD_MEMBER:
      const { groupId, member } = action.payload;
      return {
        ...state,
        groups: state.groups.map(group =>
          group.id === groupId
            ? { ...group, members: [...group.members, member] }
            : group
        ),
        activeGroup: state.activeGroup?.id === groupId
          ? { ...state.activeGroup, members: [...state.activeGroup.members, member] }
          : state.activeGroup,
      };
    case GROUP_ACTIONS.REMOVE_MEMBER:
      const { groupId: removeGroupId, memberId } = action.payload;
      return {
        ...state,
        groups: state.groups.map(group =>
          group.id === removeGroupId
            ? { ...group, members: group.members.filter(m => m.id !== memberId) }
            : group
        ),
        activeGroup: state.activeGroup?.id === removeGroupId
          ? { ...state.activeGroup, members: state.activeGroup.members.filter(m => m.id !== memberId) }
          : state.activeGroup,
      };
    case GROUP_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case GROUP_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
};

// Provider component
export const GroupProvider = ({ children }) => {
  const [state, dispatch] = useReducer(groupReducer, initialState);

  // Load groups from localStorage on mount
  useEffect(() => {
    const savedGroups = localStorage.getItem('groups');
    const savedActiveGroup = localStorage.getItem('activeGroup');
    
    if (savedGroups) {
      try {
        const groups = JSON.parse(savedGroups);
        dispatch({ type: GROUP_ACTIONS.SET_GROUPS, payload: groups });
        
        if (savedActiveGroup) {
          const activeGroup = JSON.parse(savedActiveGroup);
          dispatch({ type: GROUP_ACTIONS.SET_ACTIVE_GROUP, payload: activeGroup });
        }
      } catch (error) {
        console.error('Error loading groups from localStorage:', error);
      }
    }
  }, []);

  // Save groups to localStorage whenever groups change
  useEffect(() => {
    if (state.groups.length > 0) {
      localStorage.setItem('groups', JSON.stringify(state.groups));
    }
  }, [state.groups]);

  // Save active group to localStorage whenever it changes
  useEffect(() => {
    if (state.activeGroup) {
      localStorage.setItem('activeGroup', JSON.stringify(state.activeGroup));
    }
  }, [state.activeGroup]);

  // Actions
  const addGroup = (group) => {
    const newGroup = {
      ...group,
      id: Date.now().toString(),
      members: group.members || [],
      createdAt: new Date().toISOString(),
    };
    dispatch({ type: GROUP_ACTIONS.ADD_GROUP, payload: newGroup });
    return newGroup;
  };

  const updateGroup = (group) => {
    dispatch({ type: GROUP_ACTIONS.UPDATE_GROUP, payload: group });
  };

  const deleteGroup = (groupId) => {
    dispatch({ type: GROUP_ACTIONS.DELETE_GROUP, payload: groupId });
  };

  const setActiveGroup = (group) => {
    dispatch({ type: GROUP_ACTIONS.SET_ACTIVE_GROUP, payload: group });
  };

  const addMember = (groupId, member) => {
    const newMember = {
      ...member,
      id: Date.now().toString(),
      joinedAt: new Date().toISOString(),
    };
    dispatch({ 
      type: GROUP_ACTIONS.ADD_MEMBER, 
      payload: { groupId, member: newMember } 
    });
    return newMember;
  };

  const removeMember = (groupId, memberId) => {
    dispatch({ 
      type: GROUP_ACTIONS.REMOVE_MEMBER, 
      payload: { groupId, memberId } 
    });
  };

  const setLoading = (loading) => {
    dispatch({ type: GROUP_ACTIONS.SET_LOADING, payload: loading });
  };

  const setError = (error) => {
    dispatch({ type: GROUP_ACTIONS.SET_ERROR, payload: error });
  };

  const value = {
    ...state,
    addGroup,
    updateGroup,
    deleteGroup,
    setActiveGroup,
    addMember,
    removeMember,
    setLoading,
    setError,
  };

  return (
    <GroupContext.Provider value={value}>
      {children}
    </GroupContext.Provider>
  );
};

// Hook to use the group context
export const useGroup = () => {
  const context = useContext(GroupContext);
  if (!context) {
    throw new Error('useGroup must be used within a GroupProvider');
  }
  return context;
};
