import React from 'react';
import { Link } from 'react-router-dom';
import {
  BarChart3,
  TrendingUp,
  Users,
  Receipt,
  DollarSign
} from 'lucide-react';
import { useExpense, useGroup, useCurrency } from '../context';

const Dashboard = () => {
  const { expenses, totalExpenses } = useExpense();
  const { groups } = useGroup();
  const { formatCurrency } = useCurrency();

  // Calculate stats
  const totalGroups = groups.length;
  const totalMembers = groups.reduce((total, group) => total + group.members.length, 0);
  const averageExpense = expenses.length > 0 ? totalExpenses / expenses.length : 0;

  // Recent expenses (last 3)
  const recentExpenses = expenses
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 3);

  const stats = [
    {
      title: 'Total Expenses',
      value: formatCurrency(totalExpenses),
      icon: DollarSign,
      change: '+12%'
    },
    {
      title: 'Active Groups',
      value: totalGroups.toString(),
      icon: Users,
      change: '+2'
    },
    {
      title: 'Total Transactions',
      value: expenses.length.toString(),
      icon: Receipt,
      change: '+8'
    },
    {
      title: 'Average Expense',
      value: formatCurrency(averageExpense),
      icon: TrendingUp,
      change: '+5%'
    }
  ];

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">Rs</span>
              </div>
              <div>
                <h1 className="text-white font-bold text-xl">ExpenseSplit</h1>
                <p className="text-white/80 text-sm">Split & Pay</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
              <Link to="/group" className="text-white/90 hover:text-white transition-colors">Group</Link>
              <Link to="/expenses" className="text-white/90 hover:text-white transition-colors">Expenses</Link>
              <Link to="/dashboard" className="text-white hover:text-white transition-colors">Dashboard</Link>
              <Link to="/settings" className="text-white/90 hover:text-white transition-colors">Settings</Link>
              <div className="flex items-center space-x-2">
                <span className="text-white/90">PKR</span>
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">U</span>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center">
              <BarChart3 className="w-8 h-8 text-white" />
            </div>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Dashboard
          </h1>
          <p className="text-xl text-white/90 mb-8">
            Overview of your expenses and groups
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={stat.title} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-white/80 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-white">
                      {stat.value}
                    </p>
                    <p className="text-sm text-green-300 mt-1">
                      {stat.change}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Recent Activity Summary */}
        <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 text-center">
          <h3 className="text-xl font-semibold text-white mb-4">Recent Activity</h3>
          {recentExpenses.length === 0 ? (
            <div className="py-8">
              <Receipt className="w-12 h-12 text-white/60 mx-auto mb-3" />
              <p className="text-white/80">No recent expenses</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentExpenses.map((expense) => (
                <div key={expense.id} className="flex items-center justify-between p-3 bg-white/10 rounded-lg">
                  <div className="text-left">
                    <h4 className="font-medium text-white">{expense.description || expense.title}</h4>
                    <p className="text-sm text-white/70">
                      {expense.category} • {formatDate(expense.createdAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-white">
                      {formatCurrency(expense.amount)}
                    </p>
                    <p className="text-xs text-white/70">
                      by {expense.paidBy || 'You'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
