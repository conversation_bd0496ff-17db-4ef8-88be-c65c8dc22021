import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Receipt, 
  DollarSign,
  Calendar,
  PieChart
} from 'lucide-react';
import { GlassCard } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useExpense, useGroup, useCurrency } from '../context';

const Dashboard = () => {
  const { expenses, totalExpenses, expensesByCategory } = useExpense();
  const { groups } = useGroup();
  const { formatCurrency } = useCurrency();

  // Calculate stats
  const totalGroups = groups.length;
  const totalMembers = groups.reduce((total, group) => total + group.members.length, 0);
  const averageExpense = expenses.length > 0 ? totalExpenses / expenses.length : 0;
  
  // Recent expenses (last 5)
  const recentExpenses = expenses
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5);

  // Top categories
  const topCategories = Object.entries(expensesByCategory)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const stats = [
    {
      title: 'Total Expenses',
      value: formatCurrency(totalExpenses),
      icon: DollarSign,
      color: 'from-green-500 to-emerald-500',
      change: '+12%'
    },
    {
      title: 'Active Groups',
      value: totalGroups.toString(),
      icon: Users,
      color: 'from-blue-500 to-cyan-500',
      change: '+2'
    },
    {
      title: 'Total Transactions',
      value: expenses.length.toString(),
      icon: Receipt,
      color: 'from-purple-500 to-pink-500',
      change: '+8'
    },
    {
      title: 'Average Expense',
      value: formatCurrency(averageExpense),
      icon: TrendingUp,
      color: 'from-orange-500 to-red-500',
      change: '+5%'
    }
  ];

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <PageWrapper 
      title="Dashboard" 
      subtitle="Overview of your expenses and groups"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <GlassCard>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <p className="text-sm text-green-600 mt-1">{stat.change}</p>
                    </div>
                    <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Expenses */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <GlassCard>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Expenses</h3>
                <Receipt className="w-5 h-5 text-gray-500" />
              </div>
              
              {recentExpenses.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500">No expenses yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentExpenses.map((expense) => (
                    <div key={expense.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{expense.title}</p>
                        <p className="text-sm text-gray-500 capitalize">
                          {expense.category} • {formatDate(expense.createdAt)}
                        </p>
                      </div>
                      <span className="font-semibold text-gray-900">
                        {formatCurrency(expense.amount)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </GlassCard>
          </motion.div>

          {/* Top Categories */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <GlassCard>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Top Categories</h3>
                <PieChart className="w-5 h-5 text-gray-500" />
              </div>
              
              {topCategories.length === 0 ? (
                <div className="text-center py-8">
                  <PieChart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500">No categories yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {topCategories.map(([category, amount], index) => {
                    const percentage = totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0;
                    return (
                      <div key={category}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {category}
                          </span>
                          <span className="text-sm text-gray-600">
                            {formatCurrency(amount)} ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div
                            className="bg-gradient-to-r from-primary-500 to-purple-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${percentage}%` }}
                            transition={{ delay: 0.6 + index * 0.1, duration: 0.8 }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </GlassCard>
          </motion.div>
        </div>

        {/* Groups Overview */}
        {groups.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <GlassCard>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Active Groups</h3>
                <Users className="w-5 h-5 text-gray-500" />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groups.map((group) => (
                  <div key={group.id} className="p-4 bg-white/50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-1">{group.name}</h4>
                    <p className="text-sm text-gray-600">
                      {group.members.length} member{group.members.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                ))}
              </div>
            </GlassCard>
          </motion.div>
        )}
      </div>
    </PageWrapper>
  );
};

export default Dashboard;
