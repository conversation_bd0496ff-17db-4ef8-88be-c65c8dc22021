import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Plus,
  Users,
  Settings,
  Trash2,
  UserPlus,
  Phone,
  Mail,
  DollarSign,
  Bell,
  CheckCircle,
  Eye,
  Receipt,
  ArrowLeft
} from 'lucide-react';
import { useGroup, useCurrency } from '../context';

const Group = () => {
  const { groups, addGroup, deleteGroup, setActiveGroup, addMember, addDebt, updateDebt } = useGroup();
  const { formatCurrency } = useCurrency();

  // Simple toast function
  const showSuccess = (message) => {
    alert(`✅ ${message}`);
  };

  const showError = (message) => {
    alert(`❌ ${message}`);
  };

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isManageModalOpen, setIsManageModalOpen] = useState(false);
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [isAddDebtModalOpen, setIsAddDebtModalOpen] = useState(false);
  const [isImportContactsModalOpen, setIsImportContactsModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);

  // Form states
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newMemberName, setNewMemberName] = useState('');
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberPhone, setNewMemberPhone] = useState('');
  const [newDebtAmount, setNewDebtAmount] = useState('');
  const [newDebtDescription, setNewDebtDescription] = useState('');
  const [newDebtPayer, setNewDebtPayer] = useState('');
  const [newDebtOwer, setNewDebtOwer] = useState('');
  const [importMethod, setImportMethod] = useState('contacts');

  // View states
  const [activeTab, setActiveTab] = useState('members'); // members, debts, expenses

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      showError('Group name is required');
      return;
    }

    const group = {
      name: newGroupName.trim(),
      description: newGroupDescription.trim(),
      members: [],
      debts: [],
      expenses: [],
    };

    addGroup(group);
    showSuccess('Group created successfully!');
    setIsCreateModalOpen(false);
    setNewGroupName('');
    setNewGroupDescription('');
  };

  const handleManageGroup = (group) => {
    setSelectedGroup(group);
    setIsManageModalOpen(true);
    setActiveTab('members');
  };

  const handleAddMember = () => {
    if (!newMemberName.trim()) {
      showError('Member name is required');
      return;
    }

    const member = {
      id: Date.now().toString(),
      name: newMemberName.trim(),
      email: newMemberEmail.trim(),
      phone: newMemberPhone.trim(),
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(newMemberName.trim())}&background=random`,
      joinedAt: new Date().toISOString(),
    };

    addMember(selectedGroup.id, member);
    showSuccess('Member added successfully!');
    setIsAddMemberModalOpen(false);
    setNewMemberName('');
    setNewMemberEmail('');
    setNewMemberPhone('');
  };

  const handleAddDebt = () => {
    if (!newDebtAmount || !newDebtPayer || !newDebtOwer) {
      showError('Please fill in all required fields');
      return;
    }

    if (newDebtPayer === newDebtOwer) {
      showError('Payer and ower cannot be the same person');
      return;
    }

    const debt = {
      id: Date.now().toString(),
      amount: parseFloat(newDebtAmount),
      description: newDebtDescription.trim(),
      payer: newDebtPayer,
      ower: newDebtOwer,
      createdAt: new Date().toISOString(),
      settled: false,
    };

    addDebt(selectedGroup.id, debt);
    showSuccess('Debt added successfully!');
    setIsAddDebtModalOpen(false);
    setNewDebtAmount('');
    setNewDebtDescription('');
    setNewDebtPayer('');
    setNewDebtOwer('');
  };

  const handleImportContacts = () => {
    // Mock contact import functionality
    const mockContacts = [
      { name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' },
      { name: 'Jane Smith', email: '<EMAIL>', phone: '+1234567891' },
      { name: 'Bob Johnson', email: '<EMAIL>', phone: '+1234567892' },
    ];

    mockContacts.forEach(contact => {
      const member = {
        id: Date.now().toString() + Math.random(),
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(contact.name)}&background=random`,
        joinedAt: new Date().toISOString(),
      };
      addMember(selectedGroup.id, member);
    });

    showSuccess(`${mockContacts.length} contacts imported successfully!`);
    setIsImportContactsModalOpen(false);
  };

  const handleSendReminder = (member) => {
    // Mock reminder functionality
    const reminderOptions = [
      {
        type: 'email',
        label: 'Send Email Reminder',
        action: () => console.log(`Sending email reminder to ${member.email}`)
      },
      {
        type: 'whatsapp',
        label: 'Send WhatsApp Message',
        action: () => window.open(`https://wa.me/${member.phone?.replace(/\D/g, '')}?text=Hi ${member.name}, you have pending expenses to settle!`)
      },
      {
        type: 'sms',
        label: 'Send SMS',
        action: () => console.log(`Sending SMS to ${member.phone}`)
      }
    ];

    // Show options to user (for demo, we'll just use WhatsApp)
    reminderOptions[1].action();
    showSuccess('Reminder sent!');
  };

  const handleDeleteGroup = (groupId, groupName) => {
    if (window.confirm(`Are you sure you want to delete "${groupName}"?`)) {
      deleteGroup(groupId);
      showSuccess('Group deleted successfully');
    }
  };

  // Get the total number of members across all groups
  const totalMembers = groups.reduce((total, group) => total + group.members.length, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">Rs</span>
              </div>
              <div>
                <h1 className="text-white font-bold text-xl">ExpenseSplit</h1>
                <p className="text-white/80 text-sm">Split & Pay</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
              <Link to="/group" className="text-white hover:text-white transition-colors">Group</Link>
              <Link to="/expenses" className="text-white/90 hover:text-white transition-colors">Expenses</Link>
              <Link to="/dashboard" className="text-white/90 hover:text-white transition-colors">Dashboard</Link>
              <Link to="/settings" className="text-white/90 hover:text-white transition-colors">Settings</Link>
              <div className="flex items-center space-x-2">
                <span className="text-white/90">PKR</span>
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">U</span>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Manage Groups
          </h1>
          <p className="text-xl text-white/90 mb-8">
            Create and manage your expense groups
          </p>

          {/* Total Members Count */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 max-w-md mx-auto mb-8 border border-white/20">
            <div className="text-center">
              <div className="text-4xl font-bold text-white mb-2">
                {totalMembers}
              </div>
              <div className="text-white/80">
                Total Members
              </div>
            </div>
          </div>

          {/* Create Group Button */}
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-white/20 backdrop-blur-md hover:bg-white/30 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 border border-white/20"
          >
            <Plus className="w-5 h-5 mr-2 inline" />
            Create New Group
          </button>
        </div>

        {/* Groups Section */}
        {groups.length === 0 ? (
          <div className="text-center py-16">
            <Users className="w-16 h-16 text-white/60 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              No groups yet
            </h3>
            <p className="text-white/80 mb-6">
              Create your first group to start splitting expenses with friends.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">Your Groups</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {groups.map((group, index) => (
                <div key={group.id} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {group.name}
                      </h3>
                      {group.description && (
                        <p className="text-white/80 text-sm mb-3">
                          {group.description}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleManageGroup(group)}
                        className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteGroup(group.id, group.name)}
                        className="p-2 text-red-300 hover:text-red-200 hover:bg-red-500/20 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Group Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {group.members.length}
                      </div>
                      <div className="text-xs text-white/70">Members</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {group.expenses?.length || 0}
                      </div>
                      <div className="text-xs text-white/70">Expenses</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {formatCurrency(
                          group.debts?.reduce((sum, debt) => sum + (debt.settled ? 0 : debt.amount), 0) || 0
                        )}
                      </div>
                      <div className="text-xs text-white/70">Pending</div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setSelectedGroup(group);
                        setIsAddMemberModalOpen(true);
                      }}
                      className="flex-1 bg-white/20 hover:bg-white/30 text-white text-sm py-2 px-3 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <UserPlus className="w-4 h-4 mr-1" />
                      Add Member
                    </button>
                    <button
                      onClick={() => handleManageGroup(group)}
                      className="flex-1 bg-white/20 hover:bg-white/30 text-white text-sm py-2 px-3 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Manage
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Create Group Modal */}
        {isCreateModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 w-full max-w-md border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">Create New Group</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/90 text-sm font-medium mb-2">Group Name</label>
                  <input
                    type="text"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    placeholder="Enter group name"
                    className="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                  />
                </div>
                <div>
                  <label className="block text-white/90 text-sm font-medium mb-2">Description (Optional)</label>
                  <input
                    type="text"
                    value={newGroupDescription}
                    onChange={(e) => setNewGroupDescription(e.target.value)}
                    placeholder="What's this group for?"
                    className="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                  />
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setIsCreateModalOpen(false)}
                    className="flex-1 bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateGroup}
                    className="flex-1 bg-white/30 hover:bg-white/40 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    Create Group
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Member Modal */}
        {isAddMemberModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 w-full max-w-md border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">Add Team Member</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/90 text-sm font-medium mb-2">Name</label>
                  <input
                    type="text"
                    value={newMemberName}
                    onChange={(e) => setNewMemberName(e.target.value)}
                    placeholder="Enter member name"
                    className="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                  />
                </div>
                <div>
                  <label className="block text-white/90 text-sm font-medium mb-2">Email (Optional)</label>
                  <input
                    type="email"
                    value={newMemberEmail}
                    onChange={(e) => setNewMemberEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                  />
                </div>
                <div>
                  <label className="block text-white/90 text-sm font-medium mb-2">Phone (Optional)</label>
                  <input
                    type="tel"
                    value={newMemberPhone}
                    onChange={(e) => setNewMemberPhone(e.target.value)}
                    placeholder="Enter phone number"
                    className="w-full bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                  />
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setIsAddMemberModalOpen(false)}
                    className="flex-1 bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddMember}
                    className="flex-1 bg-white/30 hover:bg-white/40 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    Add Member
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Group;
