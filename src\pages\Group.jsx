import { motion } from 'framer-motion';
import { useState } from 'react';
import {
  Plus,
  Users,
  Settings,
  Trash2,
  Edit,
  UserPlus,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  Bell,
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Receipt,
  ArrowLeft
} from 'lucide-react';
import { GlassCard, GlassButton, Modal, Input, Select } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useGroup, useToast, useCurrency } from '../context';

const Group = () => {
  // Updated Group component - force refresh
  const { groups, addGroup, deleteGroup, setActiveGroup, addMember, addDebt, updateDebt } = useGroup();
  const { showSuccess, showError } = useToast();
  const { formatCurrency } = useCurrency();

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isManageModalOpen, setIsManageModalOpen] = useState(false);
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [isAddDebtModalOpen, setIsAddDebtModalOpen] = useState(false);
  const [isImportContactsModalOpen, setIsImportContactsModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);

  // Form states
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newMemberName, setNewMemberName] = useState('');
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberPhone, setNewMemberPhone] = useState('');
  const [newDebtAmount, setNewDebtAmount] = useState('');
  const [newDebtDescription, setNewDebtDescription] = useState('');
  const [newDebtPayer, setNewDebtPayer] = useState('');
  const [newDebtOwer, setNewDebtOwer] = useState('');
  const [importMethod, setImportMethod] = useState('contacts');

  // View states
  const [activeTab, setActiveTab] = useState('members'); // members, debts, expenses

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      showError('Group name is required');
      return;
    }

    const group = {
      name: newGroupName.trim(),
      description: newGroupDescription.trim(),
      members: [],
      debts: [],
      expenses: [],
    };

    addGroup(group);
    showSuccess('Group created successfully!');
    setIsCreateModalOpen(false);
    setNewGroupName('');
    setNewGroupDescription('');
  };

  const handleManageGroup = (group) => {
    setSelectedGroup(group);
    setIsManageModalOpen(true);
    setActiveTab('members');
  };

  const handleAddMember = () => {
    if (!newMemberName.trim()) {
      showError('Member name is required');
      return;
    }

    const member = {
      id: Date.now().toString(),
      name: newMemberName.trim(),
      email: newMemberEmail.trim(),
      phone: newMemberPhone.trim(),
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(newMemberName.trim())}&background=random`,
      joinedAt: new Date().toISOString(),
    };

    addMember(selectedGroup.id, member);
    showSuccess('Member added successfully!');
    setIsAddMemberModalOpen(false);
    setNewMemberName('');
    setNewMemberEmail('');
    setNewMemberPhone('');
  };

  const handleAddDebt = () => {
    if (!newDebtAmount || !newDebtPayer || !newDebtOwer) {
      showError('Please fill in all required fields');
      return;
    }

    if (newDebtPayer === newDebtOwer) {
      showError('Payer and ower cannot be the same person');
      return;
    }

    const debt = {
      id: Date.now().toString(),
      amount: parseFloat(newDebtAmount),
      description: newDebtDescription.trim(),
      payer: newDebtPayer,
      ower: newDebtOwer,
      createdAt: new Date().toISOString(),
      settled: false,
    };

    addDebt(selectedGroup.id, debt);
    showSuccess('Debt added successfully!');
    setIsAddDebtModalOpen(false);
    setNewDebtAmount('');
    setNewDebtDescription('');
    setNewDebtPayer('');
    setNewDebtOwer('');
  };

  const handleImportContacts = () => {
    // Mock contact import functionality
    const mockContacts = [
      { name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' },
      { name: 'Jane Smith', email: '<EMAIL>', phone: '+1234567891' },
      { name: 'Bob Johnson', email: '<EMAIL>', phone: '+1234567892' },
    ];

    mockContacts.forEach(contact => {
      const member = {
        id: Date.now().toString() + Math.random(),
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(contact.name)}&background=random`,
        joinedAt: new Date().toISOString(),
      };
      addMember(selectedGroup.id, member);
    });

    showSuccess(`${mockContacts.length} contacts imported successfully!`);
    setIsImportContactsModalOpen(false);
  };

  const handleSendReminder = (member) => {
    // Mock reminder functionality
    const reminderOptions = [
      {
        type: 'email',
        label: 'Send Email Reminder',
        action: () => console.log(`Sending email reminder to ${member.email}`)
      },
      {
        type: 'whatsapp',
        label: 'Send WhatsApp Message',
        action: () => window.open(`https://wa.me/${member.phone?.replace(/\D/g, '')}?text=Hi ${member.name}, you have pending expenses to settle!`)
      },
      {
        type: 'sms',
        label: 'Send SMS',
        action: () => console.log(`Sending SMS to ${member.phone}`)
      }
    ];

    // Show options to user (for demo, we'll just use WhatsApp)
    reminderOptions[1].action();
    showSuccess('Reminder sent!');
  };

  const handleDeleteGroup = (groupId, groupName) => {
    if (window.confirm(`Are you sure you want to delete "${groupName}"?`)) {
      deleteGroup(groupId);
      showSuccess('Group deleted successfully');
    }
  };

  // Get the total number of members across all groups
  const totalMembers = groups.reduce((total, group) => total + group.members.length, 0);

  return (
    <PageWrapper showTitle={false}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section - Recreating the old interface look */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          {/* Group Setup Icon and Title */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
            Group Setup
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Build your expense-sharing team
          </p>

          {/* Members Count Card - Recreating the old interface */}
          <GlassCard className="max-w-md mx-auto mb-8">
            <div className="text-center py-8">
              <div className="text-4xl font-bold text-blue-600 mb-2">
                {totalMembers}
              </div>
              <div className="text-gray-600">
                Members
              </div>
            </div>
          </GlassCard>

          {/* Add Team Member Button - Recreating the old interface */}
          <GlassButton
            variant="primary"
            onClick={() => setIsCreateModalOpen(true)}
            className="px-8 py-3 text-lg"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Team Member
          </GlassButton>
        </motion.div>

        {/* Groups Section */}
        {groups.length === 0 ? (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No members yet
            </h3>
            <p className="text-gray-500 mb-6">
              Add group members to start splitting expenses and
              settle debts with friends. Beautiful, fast, and completely free.
            </p>
          </motion.div>
        ) : (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Groups</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {groups.map((group, index) => (
                <motion.div
                  key={group.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <GlassCard className="h-full">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          {group.name}
                        </h3>
                        {group.description && (
                          <p className="text-gray-600 text-sm mb-3">
                            {group.description}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <GlassButton
                          variant="ghost"
                          size="sm"
                          onClick={() => handleManageGroup(group)}
                        >
                          <Settings className="w-4 h-4" />
                        </GlassButton>
                        <GlassButton
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteGroup(group.id, group.name)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </GlassButton>
                      </div>
                    </div>

                    {/* Group Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {group.members.length}
                        </div>
                        <div className="text-xs text-gray-500">Members</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {group.expenses?.length || 0}
                        </div>
                        <div className="text-xs text-gray-500">Expenses</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {formatCurrency(
                            group.debts?.reduce((sum, debt) => sum + (debt.settled ? 0 : debt.amount), 0) || 0
                          )}
                        </div>
                        <div className="text-xs text-gray-500">Pending</div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex space-x-2">
                      <GlassButton
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedGroup(group);
                          setIsAddMemberModalOpen(true);
                        }}
                        className="flex-1"
                      >
                        <UserPlus className="w-4 h-4 mr-1" />
                        Add Member
                      </GlassButton>
                      <GlassButton
                        variant="outline"
                        size="sm"
                        onClick={() => handleManageGroup(group)}
                        className="flex-1"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        Manage
                      </GlassButton>
                    </div>
                  </GlassCard>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Create Group Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Create New Group"
        >
          <div className="space-y-4">
            <Input
              label="Group Name"
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              placeholder="Enter group name"
              required
            />
            <Input
              label="Description (Optional)"
              value={newGroupDescription}
              onChange={(e) => setNewGroupDescription(e.target.value)}
              placeholder="What's this group for?"
            />
            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="outline"
                onClick={() => setIsCreateModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleCreateGroup}
                className="flex-1"
              >
                Create Group
              </GlassButton>
            </div>
          </div>
        </Modal>

        {/* Add Member Modal */}
        <Modal
          isOpen={isAddMemberModalOpen}
          onClose={() => setIsAddMemberModalOpen(false)}
          title="Add Team Member"
        >
          <div className="space-y-4">
            <Input
              label="Name"
              value={newMemberName}
              onChange={(e) => setNewMemberName(e.target.value)}
              placeholder="Enter member name"
              required
            />
            <Input
              label="Email (Optional)"
              type="email"
              value={newMemberEmail}
              onChange={(e) => setNewMemberEmail(e.target.value)}
              placeholder="Enter email address"
            />
            <Input
              label="Phone (Optional)"
              type="tel"
              value={newMemberPhone}
              onChange={(e) => setNewMemberPhone(e.target.value)}
              placeholder="Enter phone number"
            />
            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="outline"
                onClick={() => setIsAddMemberModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleAddMember}
                className="flex-1"
              >
                Add Member
              </GlassButton>
            </div>
          </div>
        </Modal>

        {/* Group Management Modal */}
        <Modal
          isOpen={isManageModalOpen}
          onClose={() => setIsManageModalOpen(false)}
          title={`Manage ${selectedGroup?.name || 'Group'}`}
          className="max-w-4xl"
        >
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              {[
                { id: 'members', label: 'Members', icon: Users },
                { id: 'debts', label: 'Debts', icon: DollarSign },
                { id: 'expenses', label: 'Expenses', icon: Receipt }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all ${
                      activeTab === tab.id
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </div>

            {/* Members Tab */}
            {activeTab === 'members' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-900">
                    Group Members ({selectedGroup?.members.length || 0})
                  </h4>
                  <div className="flex space-x-2">
                    <GlassButton
                      variant="outline"
                      size="sm"
                      onClick={() => setIsImportContactsModalOpen(true)}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Import Contacts
                    </GlassButton>
                    <GlassButton
                      variant="primary"
                      size="sm"
                      onClick={() => setIsAddMemberModalOpen(true)}
                    >
                      <UserPlus className="w-4 h-4 mr-2" />
                      Add Member
                    </GlassButton>
                  </div>
                </div>

                {selectedGroup?.members.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500">No members in this group yet</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedGroup?.members.map((member) => (
                      <div key={member.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900">{member.name}</h5>
                          <div className="text-sm text-gray-600 space-y-1">
                            {member.email && (
                              <div className="flex items-center">
                                <Mail className="w-3 h-3 mr-1" />
                                {member.email}
                              </div>
                            )}
                            {member.phone && (
                              <div className="flex items-center">
                                <Phone className="w-3 h-3 mr-1" />
                                {member.phone}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <GlassButton
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSendReminder(member)}
                            title="Send Reminder"
                          >
                            <Bell className="w-4 h-4" />
                          </GlassButton>
                          <GlassButton
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            title="Remove Member"
                          >
                            <Trash2 className="w-4 h-4" />
                          </GlassButton>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Debts Tab */}
            {activeTab === 'debts' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-900">
                    Group Debts ({selectedGroup?.debts?.length || 0})
                  </h4>
                  <GlassButton
                    variant="primary"
                    size="sm"
                    onClick={() => setIsAddDebtModalOpen(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Debt
                  </GlassButton>
                </div>

                {selectedGroup?.debts?.length === 0 ? (
                  <div className="text-center py-8">
                    <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500">No debts recorded yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {selectedGroup?.debts?.map((debt) => (
                      <div key={debt.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900">
                              {debt.payer} → {debt.ower}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              debt.settled
                                ? 'bg-green-100 text-green-800'
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {debt.settled ? 'Settled' : 'Pending'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            {debt.description && <p>{debt.description}</p>}
                            <p className="text-xs">
                              Created {new Date(debt.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold text-gray-900">
                            {formatCurrency(debt.amount)}
                          </div>
                          <div className="flex space-x-1 mt-2">
                            {!debt.settled && (
                              <GlassButton
                                variant="ghost"
                                size="sm"
                                onClick={() => updateDebt(selectedGroup.id, debt.id, { settled: true })}
                                className="text-green-600 hover:text-green-700"
                                title="Mark as Settled"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </GlassButton>
                            )}
                            <GlassButton
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              title="Delete Debt"
                            >
                              <Trash2 className="w-4 h-4" />
                            </GlassButton>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Expenses Tab */}
            {activeTab === 'expenses' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-900">
                    Group Expenses ({selectedGroup?.expenses?.length || 0})
                  </h4>
                  <GlassButton
                    variant="primary"
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Expense
                  </GlassButton>
                </div>

                {selectedGroup?.expenses?.length === 0 ? (
                  <div className="text-center py-8">
                    <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500">No expenses recorded yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {selectedGroup?.expenses?.map((expense) => (
                      <div key={expense.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900">{expense.description}</h5>
                          <div className="text-sm text-gray-600">
                            <p>Paid by {expense.paidBy}</p>
                            <p className="text-xs">
                              {new Date(expense.date).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold text-gray-900">
                            {formatCurrency(expense.amount)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </Modal>

        {/* Add Debt Modal */}
        <Modal
          isOpen={isAddDebtModalOpen}
          onClose={() => setIsAddDebtModalOpen(false)}
          title="Add Debt"
        >
          <div className="space-y-4">
            <Input
              label="Amount"
              type="number"
              step="0.01"
              value={newDebtAmount}
              onChange={(e) => setNewDebtAmount(e.target.value)}
              placeholder="0.00"
              required
            />
            <Input
              label="Description (Optional)"
              value={newDebtDescription}
              onChange={(e) => setNewDebtDescription(e.target.value)}
              placeholder="What was this for?"
            />
            <Select
              label="Who Paid?"
              value={newDebtPayer}
              onChange={(e) => setNewDebtPayer(e.target.value)}
              required
            >
              <option value="">Select payer</option>
              {selectedGroup?.members.map((member) => (
                <option key={member.id} value={member.name}>
                  {member.name}
                </option>
              ))}
            </Select>
            <Select
              label="Who Owes?"
              value={newDebtOwer}
              onChange={(e) => setNewDebtOwer(e.target.value)}
              required
            >
              <option value="">Select ower</option>
              {selectedGroup?.members.map((member) => (
                <option key={member.id} value={member.name}>
                  {member.name}
                </option>
              ))}
            </Select>
            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="outline"
                onClick={() => setIsAddDebtModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleAddDebt}
                className="flex-1"
              >
                Add Debt
              </GlassButton>
            </div>
          </div>
        </Modal>

        {/* Import Contacts Modal */}
        <Modal
          isOpen={isImportContactsModalOpen}
          onClose={() => setIsImportContactsModalOpen(false)}
          title="Import Contacts"
        >
          <div className="space-y-4">
            <div className="text-sm text-gray-600 mb-4">
              Choose how you'd like to import contacts to your group.
            </div>
            <Select
              label="Import Method"
              value={importMethod}
              onChange={(e) => setImportMethod(e.target.value)}
            >
              <option value="contacts">Phone Contacts</option>
              <option value="google">Google Contacts</option>
              <option value="csv">CSV File</option>
              <option value="manual">Manual Entry</option>
            </Select>

            {importMethod === 'contacts' && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Phone className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Phone Contacts</span>
                </div>
                <p className="text-sm text-blue-700">
                  We'll access your phone contacts to help you quickly add friends to your group.
                </p>
              </div>
            )}

            {importMethod === 'google' && (
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Mail className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">Google Contacts</span>
                </div>
                <p className="text-sm text-green-700">
                  Import contacts from your Google account.
                </p>
              </div>
            )}

            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="outline"
                onClick={() => setIsImportContactsModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleImportContacts}
                className="flex-1"
              >
                Import Contacts
              </GlassButton>
            </div>
          </div>
        </Modal>
      </div>
    </PageWrapper>
  );
};

export default Group;
