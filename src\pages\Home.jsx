import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Users, 
  Receipt, 
  BarChart3, 
  Shield, 
  Smartphone, 
  Zap,
  ArrowRight,
  Star,
  CheckCircle
} from 'lucide-react';
import { GlassCard, GlassButton } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useGroup, useExpense, useCurrency } from '../context';

const Home = () => {
  const { groups } = useGroup();
  const { expenses, totalExpenses } = useExpense();
  const { formatCurrency } = useCurrency();

  const features = [
    {
      icon: Users,
      title: 'Group Management',
      description: 'Create and manage expense groups with friends, family, or colleagues.',
      color: 'from-blue-500 to-purple-500'
    },
    {
      icon: Receipt,
      title: 'Smart Expense Tracking',
      description: 'Add expenses with categories, split them equally or customize amounts.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: BarChart3,
      title: 'Visual Analytics',
      description: 'Get insights with beautiful charts and detailed expense breakdowns.',
      color: 'from-pink-500 to-red-500'
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your data is stored locally and never shared with third parties.',
      color: 'from-green-500 to-blue-500'
    },
    {
      icon: Smartphone,
      title: 'Mobile Optimized',
      description: 'Works perfectly on all devices with responsive design.',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Instant calculations and real-time updates for seamless experience.',
      color: 'from-indigo-500 to-purple-500'
    }
  ];

  const stats = [
    { label: 'Active Groups', value: groups.length, icon: Users },
    { label: 'Total Expenses', value: expenses.length, icon: Receipt },
    { label: 'Amount Tracked', value: formatCurrency(totalExpenses), icon: BarChart3 }
  ];

  return (
    <PageWrapper showTitle={false}>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
                <span className="gradient-text">Split Expenses</span>
                <br />
                <span className="text-gray-800">Made Simple</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Track shared expenses, split bills fairly, and settle debts with friends. 
                Beautiful, fast, and completely free.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link to="/groups">
                  <GlassButton variant="primary" size="lg" className="w-full sm:w-auto">
                    Get Started
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </GlassButton>
                </Link>
                <Link to="/dashboard">
                  <GlassButton variant="secondary" size="lg" className="w-full sm:w-auto">
                    View Dashboard
                  </GlassButton>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      {(groups.length > 0 || expenses.length > 0) && (
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <GlassCard key={stat.label} className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {stat.value}
                    </h3>
                    <p className="text-gray-600">{stat.label}</p>
                  </GlassCard>
                );
              })}
            </motion.div>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold gradient-text mb-4">
              Everything You Need
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Powerful features designed to make expense splitting effortless and transparent.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                >
                  <GlassCard className="h-full">
                    <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mb-4`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {feature.description}
                    </p>
                  </GlassCard>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>
    </PageWrapper>
  );
};

export default Home;
