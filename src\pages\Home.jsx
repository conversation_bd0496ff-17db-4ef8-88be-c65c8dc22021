import React from 'react';
import { Link } from 'react-router-dom';
import { Users, Receipt, BarChart3 } from 'lucide-react';

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">Rs</span>
              </div>
              <div>
                <h1 className="text-white font-bold text-xl">ExpenseSplit</h1>
                <p className="text-white/80 text-sm">Split & Pay</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
              <Link to="/group" className="text-white/90 hover:text-white transition-colors">Group</Link>
              <Link to="/expenses" className="text-white/90 hover:text-white transition-colors">Expenses</Link>
              <Link to="/dashboard" className="text-white/90 hover:text-white transition-colors">Dashboard</Link>
              <Link to="/settings" className="text-white/90 hover:text-white transition-colors">Settings</Link>
              <div className="flex items-center space-x-2">
                <span className="text-white/90">PKR</span>
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">U</span>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Expense Splitter
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Split expenses fairly among friends and track who owes what with beautiful charts and analytics
          </p>

          {/* Feature badges */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white text-sm">Urdu Support</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-white text-sm">PWA Ready</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white text-sm">WhatsApp</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span className="text-white text-sm">Receipt OCR</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
              <span className="text-white text-sm">Recurring</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white text-sm">Analytics</span>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <Link to="/group" className="group">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white/20 transition-all duration-300 border border-white/20">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Manage Group</h3>
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white text-sm font-bold">0</span>
              </div>
            </div>
          </Link>

          <Link to="/expenses" className="group">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white/20 transition-all duration-300 border border-white/20">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <Receipt className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Track Expenses</h3>
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white text-sm font-bold">0</span>
              </div>
            </div>
          </Link>

          <Link to="/dashboard" className="group">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white/20 transition-all duration-300 border border-white/20">
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">View Dashboard</h3>
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white text-sm font-bold">Rs231.55</span>
              </div>
            </div>
          </Link>
        </div>
      </main>
    </div>
  );
};

export default Home;
