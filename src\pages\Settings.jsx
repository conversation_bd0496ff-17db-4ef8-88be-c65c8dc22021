import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Sun, 
  Monitor, 
  DollarSign, 
  Download, 
  Upload,
  Trash2,
  Shield,
  Bell
} from 'lucide-react';
import { GlassCard, GlassButton, Select } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useTheme, useCurrency, useToast } from '../context';

const Settings = () => {
  const { theme, themes, changeTheme, toggleTheme } = useTheme();
  const { currency, currencies, changeCurrency } = useCurrency();
  const { showSuccess, showError } = useToast();

  const themeOptions = [
    { value: themes.LIGHT, label: 'Light', icon: Sun },
    { value: themes.DARK, label: 'Dark', icon: Moon },
    { value: themes.SYSTEM, label: 'System', icon: Monitor }
  ];

  const currencyOptions = currencies.map(curr => ({
    value: curr.code,
    label: `${curr.name} (${curr.symbol})`
  }));

  const handleExportData = () => {
    try {
      const data = {
        groups: JSON.parse(localStorage.getItem('groups') || '[]'),
        expenses: JSON.parse(localStorage.getItem('expenses') || '[]'),
        settings: {
          theme,
          currency: currency.code
        },
        exportDate: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `expense-split-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showSuccess('Data exported successfully!');
    } catch (error) {
      showError('Failed to export data');
    }
  };

  const handleImportData = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);
        
        if (data.groups) {
          localStorage.setItem('groups', JSON.stringify(data.groups));
        }
        if (data.expenses) {
          localStorage.setItem('expenses', JSON.stringify(data.expenses));
        }
        if (data.settings) {
          if (data.settings.theme) {
            changeTheme(data.settings.theme);
          }
          if (data.settings.currency) {
            changeCurrency(data.settings.currency);
          }
        }

        showSuccess('Data imported successfully! Please refresh the page.');
      } catch (error) {
        showError('Invalid file format');
      }
    };
    reader.readAsText(file);
  };

  const handleClearAllData = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      localStorage.clear();
      showSuccess('All data cleared successfully! Please refresh the page.');
    }
  };

  const settingSections = [
    {
      title: 'Appearance',
      icon: theme === themes.DARK ? Moon : Sun,
      items: [
        {
          label: 'Theme',
          description: 'Choose your preferred theme',
          control: (
            <Select
              options={themeOptions}
              value={theme}
              onChange={changeTheme}
              className="w-48"
            />
          )
        }
      ]
    },
    {
      title: 'Currency',
      icon: DollarSign,
      items: [
        {
          label: 'Default Currency',
          description: 'Set your preferred currency for expenses',
          control: (
            <Select
              options={currencyOptions}
              value={currency.code}
              onChange={changeCurrency}
              className="w-48"
            />
          )
        }
      ]
    },
    {
      title: 'Data Management',
      icon: Shield,
      items: [
        {
          label: 'Export Data',
          description: 'Download all your data as a JSON file',
          control: (
            <GlassButton
              variant="secondary"
              onClick={handleExportData}
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </GlassButton>
          )
        },
        {
          label: 'Import Data',
          description: 'Upload a previously exported data file',
          control: (
            <div>
              <input
                type="file"
                accept=".json"
                onChange={handleImportData}
                className="hidden"
                id="import-file"
              />
              <GlassButton
                variant="secondary"
                onClick={() => document.getElementById('import-file').click()}
              >
                <Upload className="w-4 h-4 mr-2" />
                Import
              </GlassButton>
            </div>
          )
        },
        {
          label: 'Clear All Data',
          description: 'Remove all groups, expenses, and settings',
          control: (
            <GlassButton
              variant="secondary"
              onClick={handleClearAllData}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All
            </GlassButton>
          )
        }
      ]
    }
  ];

  return (
    <PageWrapper 
      title="Settings" 
      subtitle="Customize your expense splitting experience"
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {settingSections.map((section, sectionIndex) => {
            const SectionIcon = section.icon;
            return (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.1 }}
              >
                <GlassCard>
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-purple-500 rounded-lg flex items-center justify-center mr-4">
                      <SectionIcon className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {section.title}
                    </h3>
                  </div>

                  <div className="space-y-6">
                    {section.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 mb-1">
                            {item.label}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {item.description}
                          </p>
                        </div>
                        <div className="ml-4">
                          {item.control}
                        </div>
                      </div>
                    ))}
                  </div>
                </GlassCard>
              </motion.div>
            );
          })}

          {/* App Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <GlassCard>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  ExpenseSplit
                </h3>
                <p className="text-gray-600 mb-4">
                  Version 1.0.0 - A beautiful expense splitting app
                </p>
                <p className="text-sm text-gray-500">
                  Built with React, Tailwind CSS, and Framer Motion
                </p>
              </div>
            </GlassCard>
          </motion.div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default Settings;
