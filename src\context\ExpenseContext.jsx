import { createContext, useContext, useReducer, useEffect } from 'react';

const ExpenseContext = createContext();

// Action types
const EXPENSE_ACTIONS = {
  SET_EXPENSES: 'SET_EXPENSES',
  ADD_EXPENSE: 'ADD_EXPENSE',
  UPDATE_EXPENSE: 'UPDATE_EXPENSE',
  DELETE_EXPENSE: 'DELETE_EXPENSE',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
};

// Initial state
const initialState = {
  expenses: [],
  loading: false,
  error: null,
};

// Reducer
const expenseReducer = (state, action) => {
  switch (action.type) {
    case EXPENSE_ACTIONS.SET_EXPENSES:
      return {
        ...state,
        expenses: action.payload,
        loading: false,
        error: null,
      };
    case EXPENSE_ACTIONS.ADD_EXPENSE:
      return {
        ...state,
        expenses: [...state.expenses, action.payload],
        loading: false,
        error: null,
      };
    case EXPENSE_ACTIONS.UPDATE_EXPENSE:
      return {
        ...state,
        expenses: state.expenses.map(expense =>
          expense.id === action.payload.id ? action.payload : expense
        ),
        loading: false,
        error: null,
      };
    case EXPENSE_ACTIONS.DELETE_EXPENSE:
      return {
        ...state,
        expenses: state.expenses.filter(expense => expense.id !== action.payload),
        loading: false,
        error: null,
      };
    case EXPENSE_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case EXPENSE_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
};

// Provider component
export const ExpenseProvider = ({ children }) => {
  const [state, dispatch] = useReducer(expenseReducer, initialState);

  // Load expenses from localStorage on mount
  useEffect(() => {
    const savedExpenses = localStorage.getItem('expenses');
    if (savedExpenses) {
      try {
        const expenses = JSON.parse(savedExpenses);
        dispatch({ type: EXPENSE_ACTIONS.SET_EXPENSES, payload: expenses });
      } catch (error) {
        console.error('Error loading expenses from localStorage:', error);
      }
    }
  }, []);

  // Save expenses to localStorage whenever expenses change
  useEffect(() => {
    if (state.expenses.length > 0) {
      localStorage.setItem('expenses', JSON.stringify(state.expenses));
    }
  }, [state.expenses]);

  // Actions
  const addExpense = (expense) => {
    const newExpense = {
      ...expense,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };
    dispatch({ type: EXPENSE_ACTIONS.ADD_EXPENSE, payload: newExpense });
  };

  const updateExpense = (expense) => {
    dispatch({ type: EXPENSE_ACTIONS.UPDATE_EXPENSE, payload: expense });
  };

  const deleteExpense = (expenseId) => {
    dispatch({ type: EXPENSE_ACTIONS.DELETE_EXPENSE, payload: expenseId });
  };

  const setLoading = (loading) => {
    dispatch({ type: EXPENSE_ACTIONS.SET_LOADING, payload: loading });
  };

  const setError = (error) => {
    dispatch({ type: EXPENSE_ACTIONS.SET_ERROR, payload: error });
  };

  // Computed values
  const totalExpenses = state.expenses.reduce((total, expense) => total + expense.amount, 0);
  const expensesByCategory = state.expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {});

  const value = {
    ...state,
    addExpense,
    updateExpense,
    deleteExpense,
    setLoading,
    setError,
    totalExpenses,
    expensesByCategory,
  };

  return (
    <ExpenseContext.Provider value={value}>
      {children}
    </ExpenseContext.Provider>
  );
};

// Hook to use the expense context
export const useExpense = () => {
  const context = useContext(ExpenseContext);
  if (!context) {
    throw new Error('useExpense must be used within an ExpenseProvider');
  }
  return context;
};
