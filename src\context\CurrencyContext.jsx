import { createContext, useContext, useState, useEffect } from 'react';

const CurrencyContext = createContext();

// Supported currencies
export const CURRENCIES = [
  { code: 'PKR', symbol: '₨', name: 'Pakistani Rupee' },
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
];

// Provider component
export const CurrencyProvider = ({ children }) => {
  const [currency, setCurrency] = useState(CURRENCIES[0]); // Default to PKR

  // Load currency from localStorage on mount
  useEffect(() => {
    const savedCurrency = localStorage.getItem('currency');
    if (savedCurrency) {
      try {
        const currencyData = JSON.parse(savedCurrency);
        const foundCurrency = CURRENCIES.find(c => c.code === currencyData.code);
        if (foundCurrency) {
          setCurrency(foundCurrency);
        }
      } catch (error) {
        console.error('Error loading currency from localStorage:', error);
      }
    }
  }, []);

  // Save currency to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('currency', JSON.stringify(currency));
  }, [currency]);

  // Format currency amount
  const formatCurrency = (amount, showSymbol = true) => {
    if (typeof amount !== 'number') {
      amount = parseFloat(amount) || 0;
    }

    const formatted = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);

    return showSymbol ? `${currency.symbol}${formatted}` : formatted;
  };

  // Parse currency string to number
  const parseCurrency = (currencyString) => {
    if (typeof currencyString === 'number') {
      return currencyString;
    }
    
    const cleaned = currencyString.replace(/[^\d.-]/g, '');
    return parseFloat(cleaned) || 0;
  };

  // Change currency
  const changeCurrency = (currencyCode) => {
    const newCurrency = CURRENCIES.find(c => c.code === currencyCode);
    if (newCurrency) {
      setCurrency(newCurrency);
    }
  };

  const value = {
    currency,
    currencies: CURRENCIES,
    formatCurrency,
    parseCurrency,
    changeCurrency,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

// Hook to use the currency context
export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};
