import { motion } from 'framer-motion';

const FloatingBackground = () => {
  const orbs = [
    {
      id: 1,
      className: 'floating-orb-1',
      delay: 0,
      duration: 6
    },
    {
      id: 2,
      className: 'floating-orb-2',
      delay: 2,
      duration: 8
    },
    {
      id: 3,
      className: 'floating-orb-3',
      delay: 4,
      duration: 7
    }
  ];

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className={`floating-orb ${orb.className}`}
          animate={{
            y: [0, -20, 0],
            x: [0, 10, -10, 0],
            scale: [1, 1.1, 0.9, 1],
          }}
          transition={{
            duration: orb.duration,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: orb.delay,
          }}
        />
      ))}
      
      {/* Additional floating particles */}
      {Array.from({ length: 5 }).map((_, index) => (
        <motion.div
          key={`particle-${index}`}
          className="absolute w-2 h-2 bg-white/30 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -30, 0],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 4 + Math.random() * 4,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: Math.random() * 2,
          }}
        />
      ))}
    </div>
  );
};

export default FloatingBackground;
