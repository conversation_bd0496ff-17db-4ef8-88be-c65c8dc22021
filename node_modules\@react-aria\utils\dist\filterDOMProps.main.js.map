{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAID,MAAM,qCAAe,IAAI,IAAI;IAC3B;CACD;AAED,MAAM,2CAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;CACD;AAED,gCAAgC;AAChC,MAAM,sCAAgB,IAAI,IAAI;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,oCAAc,IAAI,IAAI;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qCAAe,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAmBD,MAAM,+BAAS;AAOR,SAAS,0CAAe,KAAwE,EAAE,OAAgB,CAAC,CAAC;IACzH,IAAI,aAAC,SAAS,UAAE,MAAM,UAAE,MAAM,UAAE,SAAS,mBAAQ,SAAS,EAAC,GAAG;IAC9D,IAAI,gBAAgB,CAAC;IAErB,IAAK,MAAM,QAAQ,MACjB,IACE,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAC1C,CAAA,mCAAa,GAAG,CAAC,SAChB,aAAa,yCAAmB,GAAG,CAAC,SACpC,UAAU,oCAAc,GAAG,CAAC,SAC5B,UAAU,kCAAY,GAAG,CAAC,SAC1B,UAAU,mCAAa,GAAG,CAAC,SAAU,KAAK,QAAQ,CAAC,cAAc,mCAAa,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,SACjG,sBAAA,gCAAA,UAAW,GAAG,CAAC,UACf,6BAAO,IAAI,CAAC,KAAI,GAGlB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;IAIrC,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/filterDOMProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, GlobalDOMAttributes, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\nconst globalAttrs = new Set([\n  'dir',\n  'lang',\n  'hidden',\n  'inert',\n  'translate'\n]);\n\nconst globalEvents = new Set([\n  'onClick',\n  'onAuxClick',\n  'onContextMenu',\n  'onDoubleClick',\n  'onMouseDown',\n  'onMouseEnter',\n  'onMouseLeave',\n  'onMouseMove',\n  'onMouseOut',\n  'onMouseOver',\n  'onMouseUp',\n  'onTouchCancel',\n  'onTouchEnd',\n  'onTouchMove',\n  'onTouchStart',\n  'onPointerDown',\n  'onPointerMove',\n  'onPointerUp',\n  'onPointerCancel',\n  'onPointerEnter',\n  'onPointerLeave',\n  'onPointerOver',\n  'onPointerOut',\n  'onGotPointerCapture',\n  'onLostPointerCapture',\n  'onScroll',\n  'onWheel',\n  'onAnimationStart',\n  'onAnimationEnd',\n  'onAnimationIteration',\n  'onTransitionCancel',\n  'onTransitionEnd',\n  'onTransitionRun',\n  'onTransitionStart'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /** Whether to include global DOM attributes. */\n  global?: boolean,\n  /** Whether to include DOM events. */\n  events?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps & GlobalDOMAttributes, opts: Options = {}): DOMProps & AriaLabelingProps & GlobalDOMAttributes {\n  let {labelable, isLink, global, events = global, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        (global && globalAttrs.has(prop)) ||\n        (events && globalEvents.has(prop) || (prop.endsWith('Capture') && globalEvents.has(prop.slice(0, -7)))) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n"], "names": [], "version": 3, "file": "filterDOMProps.main.js.map"}