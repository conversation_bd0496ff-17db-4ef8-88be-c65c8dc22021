@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen font-sans antialiased;
    background-attachment: fixed;
  }
}

@layer components {
  /* Glass Morphism Effects */
  .glass-effect {
    @apply bg-white/90 backdrop-blur-xl border border-white/20 shadow-glass;
  }

  .glass-card {
    @apply glass-effect rounded-2xl p-6 transition-all duration-300 hover:shadow-glass-lg hover:scale-[1.02];
  }

  .glass-button {
    @apply glass-effect rounded-xl px-6 py-3 font-medium transition-all duration-300 hover:bg-white/95 hover:shadow-glow active:scale-95;
  }

  .glass-input {
    @apply glass-effect rounded-lg px-4 py-3 w-full placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:bg-white/95;
  }

  .glass-modal {
    @apply glass-effect rounded-3xl p-8 max-w-md w-full mx-4 shadow-glass-lg;
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Animated Gradient Background */
  .animated-gradient {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100;
    background-size: 200% 200%;
    animation: gradient 15s ease infinite;
  }

  /* Button Variants */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-xl px-6 py-3 font-medium shadow-lg hover:shadow-glow transition-all duration-300 hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply glass-effect text-gray-700 rounded-xl px-6 py-3 font-medium hover:bg-white/95 transition-all duration-300 hover:scale-105 active:scale-95;
  }

  .btn-ghost {
    @apply text-gray-600 hover:text-primary-600 hover:bg-white/50 rounded-xl px-4 py-2 transition-all duration-300;
  }

  /* Floating Elements */
  .floating-orb {
    @apply absolute rounded-full opacity-20 animate-float;
  }

  .floating-orb-1 {
    @apply w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-400 top-20 left-10;
    animation-delay: 0s;
  }

  .floating-orb-2 {
    @apply w-24 h-24 bg-gradient-to-r from-pink-400 to-red-400 top-40 right-20;
    animation-delay: 2s;
  }

  .floating-orb-3 {
    @apply w-40 h-40 bg-gradient-to-r from-green-400 to-blue-400 bottom-20 left-1/4;
    animation-delay: 4s;
  }

  /* Page Transitions */
  .page-enter {
    @apply opacity-0 translate-y-4;
  }

  .page-enter-active {
    @apply opacity-100 translate-y-0 transition-all duration-300 ease-out;
  }

  .page-exit {
    @apply opacity-100 translate-y-0;
  }

  .page-exit-active {
    @apply opacity-0 -translate-y-4 transition-all duration-300 ease-in;
  }
}
