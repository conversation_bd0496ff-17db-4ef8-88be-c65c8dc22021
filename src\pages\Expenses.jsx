import { motion } from 'framer-motion';
import { useState } from 'react';
import { Plus, Receipt, Calendar, Filter } from 'lucide-react';
import { GlassCard, GlassButton, Modal, Input, Select } from '../components/ui';
import { PageWrapper } from '../components/layout';
import { useExpense, useGroup, useCurrency, useToast } from '../context';

const Expenses = () => {
  const { expenses, addExpense } = useExpense();
  const { groups, activeGroup } = useGroup();
  const { formatCurrency } = useCurrency();
  const { showSuccess, showError } = useToast();
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newExpense, setNewExpense] = useState({
    title: '',
    amount: '',
    category: '',
    groupId: '',
    description: ''
  });

  const categories = [
    { value: 'food', label: 'Food & Dining' },
    { value: 'transport', label: 'Transportation' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'shopping', label: 'Shopping' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'travel', label: 'Travel' },
    { value: 'other', label: 'Other' }
  ];

  const groupOptions = groups.map(group => ({
    value: group.id,
    label: group.name
  }));

  const handleCreateExpense = () => {
    if (!newExpense.title.trim()) {
      showError('Expense title is required');
      return;
    }
    if (!newExpense.amount || parseFloat(newExpense.amount) <= 0) {
      showError('Valid amount is required');
      return;
    }
    if (!newExpense.category) {
      showError('Category is required');
      return;
    }

    const expense = {
      ...newExpense,
      amount: parseFloat(newExpense.amount),
      title: newExpense.title.trim(),
      description: newExpense.description.trim()
    };

    addExpense(expense);
    showSuccess('Expense added successfully!');
    setIsCreateModalOpen(false);
    setNewExpense({
      title: '',
      amount: '',
      category: '',
      groupId: '',
      description: ''
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <PageWrapper 
      title="Expenses" 
      subtitle="Track and manage your shared expenses"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex items-center space-x-4">
            <GlassButton
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              Add Expense
            </GlassButton>
            <GlassButton variant="secondary">
              <Filter className="w-5 h-5 mr-2" />
              Filter
            </GlassButton>
          </div>
        </div>

        {/* Expenses List */}
        {expenses.length === 0 ? (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Receipt className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No expenses yet
            </h3>
            <p className="text-gray-500 mb-6">
              Start tracking your shared expenses by adding your first expense.
            </p>
            <GlassButton
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              Add Your First Expense
            </GlassButton>
          </motion.div>
        ) : (
          <div className="space-y-4">
            {expenses.map((expense, index) => (
              <motion.div
                key={expense.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <GlassCard>
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {expense.title}
                        </h3>
                        <span className="text-2xl font-bold gradient-text">
                          {formatCurrency(expense.amount)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="capitalize">{expense.category}</span>
                        <span className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {formatDate(expense.createdAt)}
                        </span>
                      </div>
                      {expense.description && (
                        <p className="text-gray-600 mt-2">{expense.description}</p>
                      )}
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        )}

        {/* Create Expense Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Add New Expense"
        >
          <div className="space-y-4">
            <Input
              label="Expense Title"
              placeholder="Enter expense title"
              value={newExpense.title}
              onChange={(e) => setNewExpense(prev => ({ ...prev, title: e.target.value }))}
            />
            <Input
              label="Amount"
              type="number"
              placeholder="0.00"
              value={newExpense.amount}
              onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
            />
            <Select
              label="Category"
              placeholder="Select category"
              options={categories}
              value={newExpense.category}
              onChange={(value) => setNewExpense(prev => ({ ...prev, category: value }))}
            />
            {groupOptions.length > 0 && (
              <Select
                label="Group (Optional)"
                placeholder="Select group"
                options={groupOptions}
                value={newExpense.groupId}
                onChange={(value) => setNewExpense(prev => ({ ...prev, groupId: value }))}
              />
            )}
            <Input
              label="Description (Optional)"
              placeholder="Enter description"
              value={newExpense.description}
              onChange={(e) => setNewExpense(prev => ({ ...prev, description: e.target.value }))}
            />
            <div className="flex space-x-3 pt-4">
              <GlassButton
                variant="secondary"
                onClick={() => setIsCreateModalOpen(false)}
                className="flex-1"
              >
                Cancel
              </GlassButton>
              <GlassButton
                variant="primary"
                onClick={handleCreateExpense}
                className="flex-1"
              >
                Add Expense
              </GlassButton>
            </div>
          </div>
        </Modal>
      </div>
    </PageWrapper>
  );
};

export default Expenses;
